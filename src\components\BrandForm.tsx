'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';

interface BrandFormProps {
  brandId?: number;
  isEdit?: boolean;
}

export default function BrandForm({ brandId, isEdit = false }: BrandFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    brand_photo: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    if (isEdit && brandId) {
      fetchBrand();
    }
  }, [isEdit, brandId]);

  const fetchBrand = async () => {
    try {
      const response = await axios.get(`/api/brands/${brandId}`, { withCredentials: true });
      if (response.data.success) {
        const brand = response.data.data;
        setFormData({
          name: brand.name,
          brand_photo: brand.brand_photo || ''
        });
      }
    } catch (error) {
      setError('Failed to fetch brand details');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const payload = {
        ...formData,
        brand_photo: formData.brand_photo || null
      };

      if (isEdit && brandId) {
        await axios.put(`/api/brands/${brandId}`, payload, { withCredentials: true });
      } else {
        await axios.post('/api/brands', payload, { withCredentials: true });
      }

      router.push('/admins/brands');
    } catch (error: any) {
      if (error.response?.data?.error) {
        setError(error.response.data.error);
      } else {
        setError(`Failed to ${isEdit ? 'update' : 'create'} brand`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEdit ? 'Edit Brand' : 'Create New Brand'}
        </h1>
        <a
          href="/admins/brands"
          className="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 rounded-md transition-colors"
        >
          Back to Brands
        </a>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              {error}
            </div>
          )}

          {/* Brand Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Brand Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              required
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Enter brand name"
              disabled={loading}
            />
          </div>

          {/* Brand Photo URL */}
          <div>
            <label htmlFor="brand_photo" className="block text-sm font-medium text-gray-700 mb-2">
              Brand Photo URL
            </label>
            <input
              type="url"
              id="brand_photo"
              name="brand_photo"
              value={formData.brand_photo}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="https://example.com/brand-logo.jpg"
              disabled={loading}
            />
            <p className="mt-1 text-sm text-gray-500">
              Optional: Enter a URL for the brand logo/photo
            </p>
          </div>

          {/* Preview */}
          {formData.brand_photo && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preview
              </label>
              <div className="border border-gray-300 rounded-md p-4 bg-gray-50">
                <img
                  src={formData.brand_photo}
                  alt="Brand preview"
                  className="max-h-32 max-w-full object-contain"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <a
              href="/admins/brands"
              className="px-6 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 rounded-md transition-colors"
            >
              Cancel
            </a>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white font-medium rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEdit ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                isEdit ? 'Update Brand' : 'Create Brand'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

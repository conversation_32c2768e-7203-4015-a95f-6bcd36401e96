'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';

interface BannerFormProps {
  bannerId?: number;
  isEdit?: boolean;
}

export default function BannerForm({ bannerId, isEdit = false }: BannerFormProps) {
  const [formData, setFormData] = useState({
    title: '',
    banner_image_url: '',
    redirect_url: '',
    active: true
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  useEffect(() => {
    if (isEdit && bannerId) {
      fetchBanner();
    }
  }, [isEdit, bannerId]);

  const fetchBanner = async () => {
    try {
      const response = await axios.get(`/api/banners/${bannerId}`, { withCredentials: true });
      if (response.data.success) {
        const banner = response.data.data;
        setFormData({
          title: banner.title || '',
          banner_image_url: banner.banner_image_url || '',
          redirect_url: banner.redirect_url || '',
          active: banner.active
        });
      }
    } catch (error) {
      setError('Failed to fetch banner details');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const payload = {
        title: formData.title || null,
        banner_image_url: formData.banner_image_url || null,
        redirect_url: formData.redirect_url || null,
        active: formData.active
      };

      if (isEdit && bannerId) {
        await axios.put(`/api/banners/${bannerId}`, payload, { withCredentials: true });
      } else {
        await axios.post('/api/banners', payload, { withCredentials: true });
      }

      router.push('/admins/banners');
    } catch (error: any) {
      if (error.response?.data?.error) {
        setError(error.response.data.error);
      } else {
        setError(`Failed to ${isEdit ? 'update' : 'create'} banner`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEdit ? 'Edit Banner' : 'Create New Banner'}
        </h1>
        <a
          href="/admins/banners"
          className="px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 rounded-md transition-colors"
        >
          Back to Banners
        </a>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
              {error}
            </div>
          )}

          {/* Banner Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
              Banner Title
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Enter banner title (optional)"
              disabled={loading}
            />
          </div>

          {/* Banner Image URL */}
          <div>
            <label htmlFor="banner_image_url" className="block text-sm font-medium text-gray-700 mb-2">
              Banner Image URL
            </label>
            <input
              type="url"
              id="banner_image_url"
              name="banner_image_url"
              value={formData.banner_image_url}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="https://example.com/banner-image.jpg"
              disabled={loading}
            />
            <p className="mt-1 text-sm text-gray-500">
              Optional: Enter a URL for the banner image
            </p>
          </div>

          {/* Redirect URL */}
          <div>
            <label htmlFor="redirect_url" className="block text-sm font-medium text-gray-700 mb-2">
              Redirect URL
            </label>
            <input
              type="url"
              id="redirect_url"
              name="redirect_url"
              value={formData.redirect_url}
              onChange={handleInputChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="https://example.com/landing-page"
              disabled={loading}
            />
            <p className="mt-1 text-sm text-gray-500">
              Optional: Where users will be redirected when they click the banner
            </p>
          </div>

          {/* Active Status */}
          <div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="active"
                name="active"
                checked={formData.active}
                onChange={handleInputChange}
                className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                disabled={loading}
              />
              <label htmlFor="active" className="ml-2 block text-sm text-gray-900">
                Active (banner will be displayed on the website)
              </label>
            </div>
          </div>

          {/* Preview */}
          {formData.banner_image_url && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preview
              </label>
              <div className="border border-gray-300 rounded-md p-4 bg-gray-50">
                <img
                  src={formData.banner_image_url}
                  alt="Banner preview"
                  className="max-h-48 max-w-full object-contain"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
                {formData.title && (
                  <p className="mt-2 text-sm font-medium text-gray-700">{formData.title}</p>
                )}
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <a
              href="/admins/banners"
              className="px-6 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 hover:border-gray-400 rounded-md transition-colors"
            >
              Cancel
            </a>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white font-medium rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEdit ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                isEdit ? 'Update Banner' : 'Create Banner'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

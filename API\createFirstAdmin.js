const bcrypt = require('bcrypt');
const db = require('./db');

async function createFirstAdmin() {
  try {
    console.log('Creating first admin user...');
    
    // Default admin credentials
    const username = 'admin';
    const password = 'Admin123!'; // Change this to your desired password
    
    // Check if admin already exists
    const [existingAdmins] = await db.execute('SELECT id FROM admins WHERE username = ?', [username]);
    
    if (existingAdmins.length > 0) {
      console.log('❌ Admin user already exists!');
      console.log('Use the admin dashboard to create additional admin users.');
      process.exit(1);
    }
    
    // Hash the password with bcrypt using salt rounds of 10
    console.log('Hashing password with bcrypt (salt rounds: 10)...');
    const password_hash = await bcrypt.hash(password, 10);
    
    // Insert the admin user
    const [result] = await db.execute(
      'INSERT INTO admins (username, password_hash, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
      [username, password_hash]
    );
    
    console.log('✅ First admin user created successfully!');
    console.log('');
    console.log('Login credentials:');
    console.log(`Username: ${username}`);
    console.log(`Password: ${password}`);
    console.log('');
    console.log('⚠️  IMPORTANT: Change the default password after first login!');
    console.log('');
    console.log('You can now:');
    console.log('1. Start the development server: npm run dev');
    console.log('2. Go to http://localhost:3000/login');
    console.log('3. Login with the credentials above');
    console.log('4. Create additional admin users from the dashboard');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    process.exit(1);
  } finally {
    // Close database connection
    if (db && db.end) {
      await db.end();
    }
    process.exit(0);
  }
}

// Run the script
createFirstAdmin();

'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';

interface Brand {
  id: number;
  name: string;
  brand_photo: string | null;
  created_at: string;
}

export default function BrandsList() {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchBrands();
  }, []);

  const fetchBrands = async () => {
    try {
      const response = await axios.get('/api/brands', { withCredentials: true });
      if (response.data.success) {
        setBrands(response.data.data);
      }
    } catch (error) {
      setError('Failed to fetch brands');
      console.error('Error fetching brands:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this brand?')) {
      return;
    }

    try {
      await axios.delete(`/api/brands/${id}`, { withCredentials: true });
      setBrands(brands.filter(brand => brand.id !== id));
    } catch (error) {
      setError('Failed to delete brand');
      console.error('Error deleting brand:', error);
    }
  };

  const filteredBrands = brands.filter(brand =>
    brand.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Brands Management</h1>
        <a
          href="/admins/brands/new"
          className="px-4 py-2 bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white font-medium rounded-md transition-all duration-200"
        >
          Add New Brand
        </a>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-4">
          <input
            type="text"
            placeholder="Search brands by name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Brands Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredBrands.length === 0 ? (
          <div className="col-span-full text-center py-12 text-gray-500">
            {searchTerm ? 'No brands found matching your search.' : 'No brands found. Create your first brand!'}
          </div>
        ) : (
          filteredBrands.map((brand) => (
            <div key={brand.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200">
              {/* Brand Image */}
              <div className="h-48 bg-gray-100 flex items-center justify-center">
                {brand.brand_photo ? (
                  <img
                    src={brand.brand_photo}
                    alt={brand.name}
                    className="max-h-full max-w-full object-contain"
                  />
                ) : (
                  <div className="text-gray-400 text-4xl">🏷️</div>
                )}
              </div>
              
              {/* Brand Info */}
              <div className="p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{brand.name}</h3>
                <p className="text-sm text-gray-500 mb-4">
                  Created: {new Date(brand.created_at).toLocaleDateString()}
                </p>
                
                {/* Actions */}
                <div className="flex justify-between space-x-2">
                  <a
                    href={`/admins/brands/${brand.id}/edit`}
                    className="flex-1 text-center px-3 py-2 text-yellow-600 hover:text-yellow-900 border border-yellow-300 hover:border-yellow-400 rounded-md transition-colors text-sm"
                  >
                    Edit
                  </a>
                  <button
                    onClick={() => handleDelete(brand.id)}
                    className="flex-1 px-3 py-2 text-red-600 hover:text-red-900 border border-red-300 hover:border-red-400 rounded-md transition-colors text-sm"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-sm text-gray-600">
          Showing {filteredBrands.length} of {brands.length} brands
        </div>
      </div>
    </div>
  );
}

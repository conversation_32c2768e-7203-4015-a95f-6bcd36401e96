# Admin Dashboard Setup Guide

## Password Security Implementation

The admin dashboard implements secure password handling using **bcrypt** with **10 salt rounds** for maximum security.

### How Password Hashing Works

1. **When creating admin users**: Plain passwords are hashed using `bcrypt.hash(password, 10)` before storing in the database
2. **When logging in**: Plain passwords are compared with stored hashes using `bcrypt.compare(password, hash)`
3. **Salt rounds**: We use 10 salt rounds, which provides excellent security while maintaining reasonable performance

### Security Features

- ✅ **Bcrypt hashing** with 10 salt rounds
- ✅ **Password validation** (minimum 8 characters, must contain uppercase, lowercase, and number)
- ✅ **No plain text storage** - passwords are never stored in plain text
- ✅ **Session-based authentication** with secure cookies
- ✅ **Protected routes** - admin pages require authentication
- ✅ **Automatic logout** on session expiry

## Getting Started

### 1. Setup Database
```bash
npm run setup:db
```

### 2. Create First Admin User
```bash
npm run create:admin
```

This will create an admin user with:
- **Username**: `admin`
- **Password**: `Admin123!`

⚠️ **Important**: Change the default password after first login!

### 3. Start Development Server
```bash
npm run dev
```

### 4. Access Admin Dashboard
1. Go to `http://localhost:3000/login`
2. Login with the credentials from step 2
3. You'll be redirected to the admin dashboard

## Creating Additional Admin Users

Once logged in, you can create additional admin users:

1. Go to **Dashboard** → **Create Admin User**
2. Enter username and secure password
3. Password will be automatically hashed with bcrypt (10 salt rounds)

## API Endpoints

### Authentication
- `POST /api/admin/login` - Admin login (compares plain password with bcrypt hash)
- `POST /api/admin/logout` - Admin logout

### Admin Management
- `GET /api/admins` - Get all admins (requires admin auth)
- `POST /api/admins` - Create new admin (requires admin auth, auto-hashes password)

### Password Requirements

When creating admin users, passwords must:
- Be at least 8 characters long
- Contain at least one lowercase letter (a-z)
- Contain at least one uppercase letter (A-Z)
- Contain at least one number (0-9)

## Security Best Practices

1. **Change default passwords** immediately after setup
2. **Use strong passwords** that meet the requirements
3. **Don't share admin credentials**
4. **Log out when finished** using admin dashboard
5. **Monitor admin access** through application logs

## Technical Implementation

### Backend (API)
```javascript
// Creating admin (API/database/adminCRUD.js)
const password_hash = await bcrypt.hash(password, 10);

// Login verification (API/database/adminSession.js)
const isValidPassword = await bcrypt.compare(password, hashToCompare);
```

### Frontend
- Plain passwords sent to API over HTTPS
- No password hashing on frontend (security best practice)
- Session management with React Context
- Protected routes with authentication checks

### Database Schema
```sql
CREATE TABLE admins (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,  -- Bcrypt hash stored here
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## Troubleshooting

### "Admin user already exists" error
- The first admin user has already been created
- Use the dashboard to create additional users
- Or check existing users in the database

### Login issues
- Verify username and password are correct
- Check that the API server is running
- Ensure database connection is working
- Check browser console for errors

### Password validation errors
- Ensure password meets all requirements
- Check for typos in password confirmation
- Verify password length (minimum 8 characters)

## Color Scheme

The admin dashboard uses the specified color scheme:
- **White**: #FFFFFF (backgrounds, text on dark)
- **Black**: #000000 (primary text)
- **Primary Yellow**: #E6B120 (buttons, accents)
- **Light Yellow**: #FFCD29 (hover states, highlights)

## Next Steps

After setting up the admin dashboard:
1. Create additional admin users as needed
2. Start managing products, brands, categories, and banners
3. Implement webhooks for live updates (future enhancement)
4. Add more admin features as required

For questions or issues, refer to the main README.md or check the API documentation.

'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';

interface Banner {
  id: number;
  title: string | null;
  banner_image_url: string | null;
  redirect_url: string | null;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export default function BannersList() {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchBanners();
  }, []);

  const fetchBanners = async () => {
    try {
      const response = await axios.get('/api/banners', { withCredentials: true });
      if (response.data.success) {
        setBanners(response.data.data);
      }
    } catch (error) {
      setError('Failed to fetch banners');
      console.error('Error fetching banners:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (id: number, currentStatus: boolean) => {
    try {
      await axios.put(`/api/banners/${id}`, 
        { active: !currentStatus }, 
        { withCredentials: true }
      );
      setBanners(banners.map(banner => 
        banner.id === id ? { ...banner, active: !currentStatus } : banner
      ));
    } catch (error) {
      setError('Failed to update banner status');
      console.error('Error updating banner:', error);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this banner?')) {
      return;
    }

    try {
      await axios.delete(`/api/banners/${id}`, { withCredentials: true });
      setBanners(banners.filter(banner => banner.id !== id));
    } catch (error) {
      setError('Failed to delete banner');
      console.error('Error deleting banner:', error);
    }
  };

  const filteredBanners = banners.filter(banner =>
    (banner.title || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Banners Management</h1>
        <a
          href="/admins/banners/new"
          className="px-4 py-2 bg-gradient-to-r from-yellow-600 to-yellow-500 hover:from-yellow-700 hover:to-yellow-600 text-white font-medium rounded-md transition-all duration-200"
        >
          Add New Banner
        </a>
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-4">
          <input
            type="text"
            placeholder="Search banners by title..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Banners Table */}
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Banner
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Redirect URL
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredBanners.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-12 text-center text-gray-500">
                    {searchTerm ? 'No banners found matching your search.' : 'No banners found. Create your first banner!'}
                  </td>
                </tr>
              ) : (
                filteredBanners.map((banner) => (
                  <tr key={banner.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {banner.banner_image_url && (
                          <img
                            src={banner.banner_image_url}
                            alt={banner.title || 'Banner'}
                            className="h-16 w-24 object-cover rounded-md mr-4"
                          />
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {banner.title || 'Untitled Banner'}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {banner.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleToggleActive(banner.id, banner.active)}
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          banner.active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {banner.active ? 'Active' : 'Inactive'}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {banner.redirect_url ? (
                        <a
                          href={banner.redirect_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-900 truncate max-w-xs block"
                        >
                          {banner.redirect_url}
                        </a>
                      ) : (
                        <span className="text-gray-400">No redirect</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(banner.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <a
                          href={`/admins/banners/${banner.id}/edit`}
                          className="text-yellow-600 hover:text-yellow-900 px-3 py-1 rounded-md hover:bg-yellow-50 transition-colors"
                        >
                          Edit
                        </a>
                        <button
                          onClick={() => handleDelete(banner.id)}
                          className="text-red-600 hover:text-red-900 px-3 py-1 rounded-md hover:bg-red-50 transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Summary */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-sm text-gray-600">
          Showing {filteredBanners.length} of {banners.length} banners
          {banners.length > 0 && (
            <span className="ml-4">
              Active: {banners.filter(b => b.active).length} | 
              Inactive: {banners.filter(b => !b.active).length}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
